import {
  Card,
  Chip,
  IconCard,
  Modal,
  ProgressBar,
  Typography,
} from '@/shared/components/common';
import { formatDate } from '@/shared/utils/format';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentListItemDto } from '../api/agent.api';
import { useDeleteAgent, useToggleAgentActive } from '../hooks/useAgent';
import { useAiAgentNotification } from '../hooks/useAiAgentNotification';

// Mock data đã được move sang file riêng: ../data/mockAgents.ts

interface AgentCardProps {
  agent: AgentListItemDto;
  /** Callback khi click memories */
  onMemoriesAgent?: (agentId: string) => void;
}

/**
 * Component hiển thị thông tin của một AI Agent
 */
const AgentCard: React.FC<AgentCardProps> = ({ agent, onMemoriesAgent }) => {
  const { t } = useTranslation('aiAgents');
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { updateSuccess, updateError, deleteSuccess, deleteError } = useAiAgentNotification();

  // Local state để đảm bảo UI cập nhật ngay lập tức
  const [localActive, setLocalActive] = useState(agent.active);

  // Hooks cho API calls
  const deleteAgentMutation = useDeleteAgent();
  const toggleAgentActiveMutation = useToggleAgentActive();

  // Sync local state với props khi props thay đổi
  React.useEffect(() => {
    setLocalActive(agent.active);
  }, [agent.active]);

  // Sử dụng local state để hiển thị
  const isActive = localActive;

  const handleEditAgent = () => {
    // Navigate đến trang edit agent với typeId
    console.log('AgentCard - handleEditAgent:', { agentId: agent.id, typeId: agent.typeId });
    navigate(`/ai-agents/${agent.id}/edit?typeId=${agent.typeId}`);
  };

  const handleMemoriesClick = () => {
    if (onMemoriesAgent) {
      onMemoriesAgent(agent.id);
    }
  };

  const handleToggleActive = async () => {
    const previousState = isActive;

    try {
      // Cập nhật UI ngay lập tức
      setLocalActive(!isActive);

      // Gọi API với optimistic update
      await toggleAgentActiveMutation.mutateAsync(agent.id);

      updateSuccess(
        previousState
          ? t('notification.titles.deactivateSuccess', 'Agent đã được bật')
          : t('notification.titles.activateSuccess', 'Agent đã được tắt')
      );
    } catch (error) {
      console.error('Error toggling agent active:', error);

      // Rollback local state nếu API thất bại
      setLocalActive(previousState);

      updateError(
        'Agent',
        error instanceof Error
          ? error.message
          : t('aiAgents.toggleError', 'Có lỗi xảy ra khi thay đổi trạng thái agent')
      );
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);
      setShowDeleteModal(false);

      deleteSuccess('Agent');
    } catch (error) {
      console.error('Error deleting agent:', error);
      deleteError('Agent', error instanceof Error ? error.message : undefined);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Tính toán phần trăm kinh nghiệm từ dữ liệu thực
  const currentExp = parseInt(agent.exp) || 0;
  const maxExp = parseInt(agent.expMax) || 1;
  const experiencePercent = Math.round((currentExp / maxExp) * 100);

  // Format ngày tạo
  const createdDate = formatDate(agent.createdAt, 'DD/MM/YYYY');

  return (
    <>
      <Card
        className="h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
        variant="elevated"
      >
        <div className="p-4">
          <div className="flex flex-col space-y-4">
            {/* Hàng 1: Avatar, tên, loại agent và model */}
            <div className="flex items-center gap-3 overflow-hidden">
              {/* Avatar và khung level */}
              <div className="relative w-16 h-16 sm:w-20 sm:h-20 flex-shrink-0">
                <div className="w-full h-full relative">
                  <img
                    src={agent.badgeUrl}
                    alt="Level frame"
                    className="absolute inset-0 w-full h-full object-contain z-10"
                  />
                  <div className="absolute inset-0 flex items-center justify-center z-0">
                    <img
                      src={agent.avatar || '/assets/images/default-avatar.png'}
                      alt={agent.name}
                      className="w-[75%] h-[75%] rounded-full object-cover"
                    />
                  </div>
                  {/* Chỉ báo trạng thái active */}
                  <div
                    className={`absolute bottom-1 right-1 w-3 h-3 rounded-full z-20 ${isActive ? 'bg-green-500 dark:bg-green-400' : 'bg-gray-300 dark:bg-gray-600' 
                      }`}
                  />
                </div>
              </div>

              {/* Thông tin agent: tên, loại và model */}
              <div className="flex flex-col min-w-0 flex-grow">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                  <div className="min-w-0">
                    <Typography
                      variant="h5"
                      className="font-semibold text-gray-900 dark:text-white truncate"
                    >
                      {agent.name}
                    </Typography>
                    <div className="text-xs text-gray-500 dark:text-gray-400">{agent.typeName}</div>
                    <div className="text-xs text-gray-400 dark:text-gray-500">{createdDate}</div>
                  </div>
                  <div className="flex-shrink-0 mt-1 sm:mt-0">
                    <Chip variant="primary" size="sm" className="font-normal max-w-full truncate">
                      {agent.modelId}
                    </Chip>
                  </div>
                </div>
              </div>
            </div>

            {/* Hàng 2: Level/exp và các nút chức năng */}
            <div className="flex flex-col">
              {/* Thông tin level và exp */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                    Level {agent.level} • {currentExp}/{maxExp} EXP
                  </span>
                  <Chip
                    variant={
                      experiencePercent >= 80
                        ? 'success'
                        : experiencePercent >= 50
                          ? 'warning'
                          : 'danger'
                    }
                    size="sm"
                    className="font-medium text-xs"
                  >
                    {experiencePercent}%
                  </Chip>
                </div>
                <ProgressBar
                  value={experiencePercent}
                  size="sm"
                  color={
                    experiencePercent >= 80
                      ? 'success'
                      : experiencePercent >= 50
                        ? 'warning'
                        : 'primary'
                  }
                  gradient
                  rounded
                  className="mb-3"
                />
              </div>

              {/* Các nút chức năng */}
              <div className="flex justify-end space-x-6">
                <IconCard
                  icon="power"
                  variant={isActive ? 'primary' : 'default'}
                  size="md"
                  onClick={handleToggleActive}
                  className={isActive ? 'text-green-500' : 'text-gray-400'}
                  disabled={toggleAgentActiveMutation.isPending}
                  title={isActive ? t('common.activate') : t('common.deactivate')}
                  tooltipPosition="top"
                />
                <IconCard
                  icon="clock"
                  variant="default"
                  size="md"
                  onClick={handleMemoriesClick}
                  title={t('aiAgents:memories.title', 'Memories')}
                  tooltipPosition="top"
                />
                <IconCard
                  icon="edit"
                  variant="default"
                  size="md"
                  onClick={handleEditAgent}
                  title={t('common.edit')}
                  tooltipPosition="top"
                />
                <IconCard
                  icon="trash"
                  variant="default"
                  size="md"
                  onClick={handleDeleteClick}
                  className="text-red-500 hover:text-red-600"
                  disabled={deleteAgentMutation.isPending}
                  title={t('common.delete')}
                  tooltipPosition="top"
                />
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('deleteConfirmTitle', 'Xác nhận xóa Agent')}
        size="md"
        footer={
          <div className="flex justify-end gap-3">

            <IconCard
              icon="trash"
              title={t('common.delete', 'Xóa')}
              onClick={handleDeleteConfirm}
              variant="danger"
              disabled={deleteAgentMutation.isPending}
            />

          </div>
        }
      >
        <div className="space-y-4">
          

          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={agent.avatar || '/assets/images/default-avatar.png'}
                alt={agent.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{agent.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{agent.typeName}</p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AgentCard;

// Mock data đã được move sang file riêng: ../data/mockAgents.ts
// AgentCardDemo component đã được move sang file riêng để tránh fast refresh warning
