import {
  Card,
  Chip,
  IconCard,
  Modal,
  ProgressBar,
  Typography,
} from '@/shared/components/common';
import { formatDate } from '@/shared/utils/format';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentListItemDto } from '../api/agent.api';
import { useDeleteAgent, useToggleAgentActive } from '../hooks/useAgent';
import { useAiAgentNotification } from '../hooks/useAiAgentNotification';

// Mock data đã được move sang file riêng: ../data/mockAgents.ts

interface AgentCardProps {
  agent: AgentListItemDto;
  /** Callback khi click memories */
  onMemoriesAgent?: (agentId: string) => void;
}

/**
 * Component hiển thị thông tin của một AI Agent
 */
const AgentCard: React.FC<AgentCardProps> = ({ agent, onMemoriesAgent }) => {
  const { t } = useTranslation('aiAgents');
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { updateSuccess, updateError, deleteSuccess, deleteError } = useAiAgentNotification();

  // Local state để đảm bảo UI cập nhật ngay lập tức
  const [localActive, setLocalActive] = useState(agent.active);

  // Hooks cho API calls
  const deleteAgentMutation = useDeleteAgent();
  const toggleAgentActiveMutation = useToggleAgentActive();

  // Sync local state với props khi props thay đổi
  React.useEffect(() => {
    setLocalActive(agent.active);
  }, [agent.active]);

  // Sử dụng local state để hiển thị
  const isActive = localActive;

  const handleEditAgent = () => {
    // Navigate đến trang edit agent với typeId
    console.log('AgentCard - handleEditAgent:', { agentId: agent.id, typeId: agent.typeId });
    navigate(`/ai-agents/${agent.id}/edit?typeId=${agent.typeId}`);
  };

  const handleMemoriesClick = () => {
    if (onMemoriesAgent) {
      onMemoriesAgent(agent.id);
    }
  };

  const handleToggleActive = async () => {
    const previousState = isActive;

    try {
      // Cập nhật UI ngay lập tức
      setLocalActive(!isActive);

      // Gọi API với optimistic update
      await toggleAgentActiveMutation.mutateAsync(agent.id);

      updateSuccess(
        previousState
          ? t('notification.titles.deactivateSuccess', 'Agent đã được bật')
          : t('notification.titles.activateSuccess', 'Agent đã được tắt')
      );
    } catch (error) {
      console.error('Error toggling agent active:', error);

      // Rollback local state nếu API thất bại
      setLocalActive(previousState);

      updateError(
        'Agent',
        error instanceof Error
          ? error.message
          : t('aiAgents.toggleError', 'Có lỗi xảy ra khi thay đổi trạng thái agent')
      );
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);
      setShowDeleteModal(false);

      deleteSuccess('Agent');
    } catch (error) {
      console.error('Error deleting agent:', error);
      deleteError('Agent', error instanceof Error ? error.message : undefined);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Tính toán phần trăm kinh nghiệm từ dữ liệu thực
  const currentExp = parseInt(agent.exp) || 0;
  const maxExp = parseInt(agent.expMax) || 1;
  const experiencePercent = Math.round((currentExp / maxExp) * 100);

  // Format ngày tạo
  const createdDate = formatDate(agent.createdAt, 'DD/MM/YYYY');

  return (
    <>
      <Card
        className="h-full overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 border-0"
        variant="elevated"
      >
        <div className="p-5">
          <div className="flex flex-col space-y-4">
            {/* Header: Avatar và thông tin chính - Bố cục ngang cân đối */}
            <div className="flex items-center gap-4">
              {/* Avatar và khung level */}
              <div className="relative w-20 h-20 flex-shrink-0">
                <div className="w-full h-full relative">
                  {/* Badge frame */}
                  <img
                    src={agent.badgeUrl}
                    alt="Level frame"
                    className="absolute inset-0 w-full h-full object-contain z-10 drop-shadow-lg"
                  />
                  {/* Avatar */}
                  <div className="absolute inset-0 flex items-center justify-center z-0">
                    <img
                      src={agent.avatar || '/assets/images/default-avatar.png'}
                      alt={agent.name}
                      className="w-[70%] h-[70%] rounded-full object-cover shadow-md ring-2 ring-white/20"
                    />
                  </div>
                  {/* Status indicator */}
                  <div
                    className={`absolute bottom-1 right-1 w-3 h-3 rounded-full z-20 border-2 border-white shadow-sm ${
                      isActive
                        ? 'bg-green-500 dark:bg-green-400'
                        : 'bg-gray-400 dark:bg-gray-600'
                    }`}
                  />
                </div>
              </div>

              {/* Thông tin chính - Bố cục dọc */}
              <div className="flex flex-col min-w-0 flex-grow space-y-1">
                {/* Hàng 1: Tên agent */}
                <Typography
                  variant="h5"
                  className="font-bold text-gray-900 dark:text-white truncate text-lg leading-tight"
                >
                  {agent.name}
                </Typography>

                {/* Hàng 2: Type và ngày tạo */}
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                    {agent.typeName}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {createdDate}
                  </div>
                </div>

                {/* Hàng 3: Model chip */}
                <div className="flex justify-end">
                  <Chip
                    variant="primary"
                    size="sm"
                    className="font-semibold px-3 py-1 bg-gradient-to-r from-red-500 to-red-600 text-white shadow-md"
                  >
                    {agent.modelId}
                  </Chip>
                </div>
              </div>
            </div>

            {/* Level và Experience Bar - Compact design */}
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                  Level {agent.level} • {currentExp}/{maxExp} EXP
                </span>
                <Chip
                  variant={
                    experiencePercent >= 80
                      ? 'success'
                      : experiencePercent >= 50
                        ? 'warning'
                        : 'danger'
                  }
                  size="sm"
                  className="font-bold text-xs px-2 py-1"
                >
                  {experiencePercent}%
                </Chip>
              </div>
              <ProgressBar
                value={experiencePercent}
                size="sm"
                color={
                  experiencePercent >= 80
                    ? 'success'
                    : experiencePercent >= 50
                      ? 'warning'
                      : 'primary'
                }
                gradient
                rounded
                className="h-2 shadow-inner"
              />
            </div>

            {/* Action Buttons - Compact spacing */}
            <div className="flex justify-center space-x-6 pt-1">
              <IconCard
                icon="power"
                variant={isActive ? 'primary' : 'default'}
                size="md"
                onClick={handleToggleActive}
                className={`transition-all duration-200 ${
                  isActive
                    ? 'text-green-500 hover:text-green-600 hover:scale-105'
                    : 'text-gray-400 hover:text-gray-600 hover:scale-105'
                }`}
                disabled={toggleAgentActiveMutation.isPending}
                title={isActive ? t('common.deactivate') : t('common.activate')}
                tooltipPosition="top"
              />
              <IconCard
                icon="clock"
                variant="default"
                size="md"
                onClick={handleMemoriesClick}
                className="text-blue-500 hover:text-blue-600 hover:scale-105 transition-all duration-200"
                title={t('aiAgents:memories.title', 'Memories')}
                tooltipPosition="top"
              />
              <IconCard
                icon="edit"
                variant="default"
                size="md"
                onClick={handleEditAgent}
                className="text-amber-500 hover:text-amber-600 hover:scale-105 transition-all duration-200"
                title={t('common.edit')}
                tooltipPosition="top"
              />
              <IconCard
                icon="trash"
                variant="default"
                size="md"
                onClick={handleDeleteClick}
                className="text-red-500 hover:text-red-600 hover:scale-105 transition-all duration-200"
                disabled={deleteAgentMutation.isPending}
                title={t('common.delete')}
                tooltipPosition="top"
              />
            </div>
          </div>
        </div>
      </Card>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('deleteConfirmTitle', 'Xác nhận xóa Agent')}
        size="md"
        footer={
          <div className="flex justify-end gap-3">

            <IconCard
              icon="trash"
              title={t('common.delete', 'Xóa')}
              onClick={handleDeleteConfirm}
              variant="danger"
              disabled={deleteAgentMutation.isPending}
            />

          </div>
        }
      >
        <div className="space-y-4">
          

          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={agent.avatar || '/assets/images/default-avatar.png'}
                alt={agent.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{agent.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{agent.typeName}</p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AgentCard;

// Mock data đã được move sang file riêng: ../data/mockAgents.ts
// AgentCardDemo component đã được move sang file riêng để tránh fast refresh warning
