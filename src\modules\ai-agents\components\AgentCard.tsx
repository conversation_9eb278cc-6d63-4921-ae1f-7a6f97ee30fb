import {
  Card,
  Chip,
  IconCard,
  Modal,
  ProgressBar,
  Typography,
} from '@/shared/components/common';
import { formatDate } from '@/shared/utils/format';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentListItemDto } from '../api/agent.api';
import { useDeleteAgent, useToggleAgentActive } from '../hooks/useAgent';
import { useAiAgentNotification } from '../hooks/useAiAgentNotification';

// Mock data đã được move sang file riêng: ../data/mockAgents.ts

interface AgentCardProps {
  agent: AgentListItemDto;
  /** Callback khi click memories */
  onMemoriesAgent?: (agentId: string) => void;
}

/**
 * Component hiển thị thông tin của một AI Agent
 */
const AgentCard: React.FC<AgentCardProps> = ({ agent, onMemoriesAgent }) => {
  const { t } = useTranslation('aiAgents');
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { updateSuccess, updateError, deleteSuccess, deleteError } = useAiAgentNotification();

  // Local state để đảm bảo UI cập nhật ngay lập tức
  const [localActive, setLocalActive] = useState(agent.active);

  // Hooks cho API calls
  const deleteAgentMutation = useDeleteAgent();
  const toggleAgentActiveMutation = useToggleAgentActive();

  // Sync local state với props khi props thay đổi
  React.useEffect(() => {
    setLocalActive(agent.active);
  }, [agent.active]);

  // Sử dụng local state để hiển thị
  const isActive = localActive;

  const handleEditAgent = () => {
    // Navigate đến trang edit agent với typeId
    console.log('AgentCard - handleEditAgent:', { agentId: agent.id, typeId: agent.typeId });
    navigate(`/ai-agents/${agent.id}/edit?typeId=${agent.typeId}`);
  };

  const handleMemoriesClick = () => {
    if (onMemoriesAgent) {
      onMemoriesAgent(agent.id);
    }
  };

  const handleToggleActive = async () => {
    const previousState = isActive;

    try {
      // Cập nhật UI ngay lập tức
      setLocalActive(!isActive);

      // Gọi API với optimistic update
      await toggleAgentActiveMutation.mutateAsync(agent.id);

      updateSuccess(
        previousState
          ? t('notification.titles.deactivateSuccess', 'Agent đã được bật')
          : t('notification.titles.activateSuccess', 'Agent đã được tắt')
      );
    } catch (error) {
      console.error('Error toggling agent active:', error);

      // Rollback local state nếu API thất bại
      setLocalActive(previousState);

      updateError(
        'Agent',
        error instanceof Error
          ? error.message
          : t('aiAgents.toggleError', 'Có lỗi xảy ra khi thay đổi trạng thái agent')
      );
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);
      setShowDeleteModal(false);

      deleteSuccess('Agent');
    } catch (error) {
      console.error('Error deleting agent:', error);
      deleteError('Agent', error instanceof Error ? error.message : undefined);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Tính toán phần trăm kinh nghiệm từ dữ liệu thực
  const currentExp = parseInt(agent.exp) || 0;
  const maxExp = parseInt(agent.expMax) || 1;
  const experiencePercent = Math.round((currentExp / maxExp) * 100);

  // Format ngày tạo
  const createdDate = formatDate(agent.createdAt, 'DD/MM/YYYY');

  return (
    <>
      <Card
        className="h-full overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gradient-to-br dark:from-slate-800 dark:to-slate-900 border border-gray-200 dark:border-slate-700"
        variant="elevated"
      >
        <div className="p-6">
          <div className="flex flex-col space-y-5">
            {/* Header: Avatar lớn và thông tin chính */}
            <div className="flex items-start gap-5">
              {/* Avatar và khung level - Tăng kích thước lớn hơn */}
              <div className="relative w-28 h-28 flex-shrink-0">
                <div className="w-full h-full relative">
                  {/* Badge frame */}
                  <img
                    src={agent.badgeUrl}
                    alt="Level frame"
                    className="absolute inset-0 w-full h-full object-contain z-10 drop-shadow-xl"
                  />
                  {/* Avatar */}
                  <div className="absolute inset-0 flex items-center justify-center z-0">
                    <img
                      src={agent.avatar || '/assets/images/default-avatar.png'}
                      alt={agent.name}
                      className="w-[68%] h-[68%] rounded-full object-cover shadow-lg ring-3 ring-white/30 dark:ring-white/20"
                    />
                  </div>
                  {/* Status indicator - Lớn hơn */}
                  <div
                    className={`absolute bottom-2 right-2 w-5 h-5 rounded-full z-20 border-3 border-white dark:border-slate-800 shadow-lg ${
                      isActive
                        ? 'bg-green-500 dark:bg-green-400'
                        : 'bg-gray-400 dark:bg-gray-600'
                    }`}
                  />
                </div>
              </div>

              {/* Thông tin chính */}
              <div className="flex flex-col min-w-0 flex-grow space-y-3">
                {/* Tên agent */}
                <div>
                  <Typography
                    variant="h4"
                    className="font-bold text-gray-900 dark:text-white truncate text-xl leading-tight mb-1"
                  >
                    {agent.name}
                  </Typography>
                  <div className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                    {agent.typeName}
                  </div>
                </div>

                {/* Ngày tạo và Model chip */}
                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                    {createdDate}
                  </div>
                  <Chip
                    variant="primary"
                    size="md"
                    className="font-bold px-4 py-2 bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg text-sm"
                  >
                    {agent.modelId}
                  </Chip>
                </div>
              </div>
            </div>

            {/* Level và Experience Bar - Thiết kế đẹp hơn */}
            <div className="bg-gray-50 dark:bg-slate-800/50 rounded-xl p-4 space-y-3">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">{agent.level}</span>
                  </div>
                  <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                    Level {agent.level} • {currentExp}/{maxExp} EXP
                  </span>
                </div>
                <Chip
                  variant={
                    experiencePercent >= 80
                      ? 'success'
                      : experiencePercent >= 50
                        ? 'warning'
                        : 'danger'
                  }
                  size="sm"
                  className="font-bold text-sm px-3 py-1 shadow-md"
                >
                  {experiencePercent}%
                </Chip>
              </div>
              <div className="relative">
                <ProgressBar
                  value={experiencePercent}
                  size="md"
                  color={
                    experiencePercent >= 80
                      ? 'success'
                      : experiencePercent >= 50
                        ? 'warning'
                        : 'primary'
                  }
                  gradient
                  rounded
                  className="h-3 shadow-inner bg-gray-200 dark:bg-slate-700"
                />
                {/* Glow effect cho progress bar */}
                <div
                  className={`absolute top-0 left-0 h-3 rounded-full opacity-50 blur-sm ${
                    experiencePercent >= 80
                      ? 'bg-green-400'
                      : experiencePercent >= 50
                        ? 'bg-yellow-400'
                        : 'bg-blue-400'
                  }`}
                  style={{ width: `${experiencePercent}%` }}
                />
              </div>
            </div>

            {/* Action Buttons - Thiết kế tròn đẹp hơn */}
            <div className="flex justify-center space-x-6 pt-3">
              <button
                onClick={handleToggleActive}
                disabled={toggleAgentActiveMutation.isPending}
                className={`w-14 h-14 rounded-full flex items-center justify-center transition-all duration-300 ${
                  isActive
                    ? 'bg-green-500/10 text-green-500 hover:bg-green-500/20 hover:scale-110'
                    : 'bg-gray-500/10 text-gray-400 dark:text-gray-300 hover:bg-gray-500/20 hover:scale-110'
                }`}
                title={isActive ? t('common.deactivate') : t('common.activate')}
              >
                <Icon name="power" size="lg" className="drop-shadow-md" />
              </button>

              <button
                onClick={handleMemoriesClick}
                className="w-14 h-14 rounded-full bg-blue-500/10 text-blue-500 hover:bg-blue-500/20 hover:scale-110 flex items-center justify-center transition-all duration-300"
                title={t('aiAgents:memories.title', 'Memories')}
              >
                <Icon name="clock" size="lg" className="drop-shadow-md" />
              </button>

              <button
                onClick={handleEditAgent}
                className="w-14 h-14 rounded-full bg-amber-500/10 text-amber-500 hover:bg-amber-500/20 hover:scale-110 flex items-center justify-center transition-all duration-300"
                title={t('common.edit')}
              >
                <Icon name="edit" size="lg" className="drop-shadow-md" />
              </button>

              <button
                onClick={handleDeleteClick}
                disabled={deleteAgentMutation.isPending}
                className="w-14 h-14 rounded-full bg-red-500/10 text-red-500 hover:bg-red-500/20 hover:scale-110 flex items-center justify-center transition-all duration-300"
                title={t('common.delete')}
              >
                <Icon name="trash" size="lg" className="drop-shadow-md" />
              </button>
            </div>
          </div>
        </div>
      </Card>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('deleteConfirmTitle', 'Xác nhận xóa Agent')}
        size="md"
        footer={
          <div className="flex justify-end gap-3">

            <IconCard
              icon="trash"
              title={t('common.delete', 'Xóa')}
              onClick={handleDeleteConfirm}
              variant="danger"
              disabled={deleteAgentMutation.isPending}
            />

          </div>
        }
      >
        <div className="space-y-4">
          

          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={agent.avatar || '/assets/images/default-avatar.png'}
                alt={agent.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{agent.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{agent.typeName}</p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AgentCard;

// Mock data đã được move sang file riêng: ../data/mockAgents.ts
// AgentCardDemo component đã được move sang file riêng để tránh fast refresh warning
