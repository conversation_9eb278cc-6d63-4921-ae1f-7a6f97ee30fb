import {
  Card,
  Icon,
  IconCard,
  Modal,
  Typography,
} from '@/shared/components/common';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentListItemDto } from '../api/agent.api';
import { useDeleteAgent, useToggleAgentActive } from '../hooks/useAgent';
import { useAiAgentNotification } from '../hooks/useAiAgentNotification';

// Mock data đã được move sang file riêng: ../data/mockAgents.ts

interface AgentCardProps {
  agent: AgentListItemDto;
  /** Callback khi click memories */
  onMemoriesAgent?: (agentId: string) => void;
}

/**
 * Component hiển thị thông tin của một AI Agent
 */
const AgentCard: React.FC<AgentCardProps> = ({ agent, onMemoriesAgent }) => {
  const { t } = useTranslation('aiAgents');
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { updateSuccess, updateError, deleteSuccess, deleteError } = useAiAgentNotification();

  // Local state để đảm bảo UI cập nhật ngay lập tức
  const [localActive, setLocalActive] = useState(agent.active);

  // Hooks cho API calls
  const deleteAgentMutation = useDeleteAgent();
  const toggleAgentActiveMutation = useToggleAgentActive();

  // Sync local state với props khi props thay đổi
  React.useEffect(() => {
    setLocalActive(agent.active);
  }, [agent.active]);

  // Sử dụng local state để hiển thị
  const isActive = localActive;

  const handleEditAgent = () => {
    // Navigate đến trang edit agent với typeId
    console.log('AgentCard - handleEditAgent:', { agentId: agent.id, typeId: agent.typeId });
    navigate(`/ai-agents/${agent.id}/edit?typeId=${agent.typeId}`);
  };

  const handleMemoriesClick = () => {
    if (onMemoriesAgent) {
      onMemoriesAgent(agent.id);
    }
  };

  const handleToggleActive = async () => {
    const previousState = isActive;

    try {
      // Cập nhật UI ngay lập tức
      setLocalActive(!isActive);

      // Gọi API với optimistic update
      await toggleAgentActiveMutation.mutateAsync(agent.id);

      updateSuccess(
        previousState
          ? t('notification.titles.deactivateSuccess', 'Agent đã được bật')
          : t('notification.titles.activateSuccess', 'Agent đã được tắt')
      );
    } catch (error) {
      console.error('Error toggling agent active:', error);

      // Rollback local state nếu API thất bại
      setLocalActive(previousState);

      updateError(
        'Agent',
        error instanceof Error
          ? error.message
          : t('aiAgents.toggleError', 'Có lỗi xảy ra khi thay đổi trạng thái agent')
      );
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);
      setShowDeleteModal(false);

      deleteSuccess('Agent');
    } catch (error) {
      console.error('Error deleting agent:', error);
      deleteError('Agent', error instanceof Error ? error.message : undefined);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Tính toán phần trăm kinh nghiệm từ dữ liệu thực
  const currentExp = parseInt(agent.exp) || 0;
  const maxExp = parseInt(agent.expMax) || 1;
  const experiencePercent = Math.round((currentExp / maxExp) * 100);



  return (
    <>
      <div className="relative h-full group">
        <Card
          className="h-full overflow-hidden bg-slate-900/95 backdrop-blur-sm border-0 shadow-2xl hover:shadow-purple-500/20 transition-all duration-500 hover:scale-[1.02] relative"
          variant="elevated"
        >
          {/* Animated background gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-600/10 via-blue-600/5 to-cyan-600/10 opacity-60" />
          <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/[0.02] to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

          {/* Glowing border effect */}
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-cyan-500/20 p-[1px] opacity-0 group-hover:opacity-100 transition-opacity duration-500">
            <div className="w-full h-full bg-slate-900 rounded-lg" />
          </div>
          <div className="p-6 relative z-10">
            {/* Header với avatar và tên */}
            <div className="text-center mb-6">
              {/* Avatar với hiệu ứng đặc biệt */}
              <div className="relative inline-block mb-4">
                <div className="relative w-24 h-24 mx-auto">
                  {/* Rotating border effect */}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500 via-blue-500 to-cyan-500 p-[2px] animate-spin-slow">
                    <div className="w-full h-full rounded-full bg-slate-900" />
                  </div>

                  {/* Badge frame */}
                  <img
                    src={agent.badgeUrl}
                    alt="Level frame"
                    className="absolute inset-0 w-full h-full object-contain z-20 drop-shadow-2xl"
                  />

                  {/* Avatar */}
                  <div className="absolute inset-0 flex items-center justify-center z-10">
                    <img
                      src={agent.avatar || '/assets/images/default-avatar.png'}
                      alt={agent.name}
                      className="w-[75%] h-[75%] rounded-full object-cover shadow-2xl ring-2 ring-white/30"
                    />
                  </div>

                  {/* Status với pulse effect */}
                  <div className={`absolute bottom-1 right-1 w-5 h-5 rounded-full z-30 border-3 border-slate-900 ${
                    isActive
                      ? 'bg-emerald-400 shadow-lg shadow-emerald-400/50 animate-pulse'
                      : 'bg-slate-500'
                  }`} />
                </div>
              </div>

              {/* Tên agent */}
              <Typography
                variant="h5"
                className="font-bold text-white mb-2 text-lg leading-tight"
              >
                {agent.name}
              </Typography>

              {/* Model ID */}
              <div className="text-sm text-slate-300 font-medium mb-4 opacity-80">
                {agent.modelId}
              </div>
            </div>

            {/* Stats section */}
            <div className="space-y-4">
              {/* Level và EXP */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl shadow-purple-500/25">
                    <span className="text-white font-bold text-lg">{agent.level}</span>
                  </div>
                  <div>
                    <div className="text-white font-semibold text-sm">Level {agent.level}</div>
                    <div className="text-slate-400 text-xs">Agent Rank</div>
                  </div>
                </div>
              </div>

              {/* Progress bar */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-slate-300 text-sm font-medium">Experience</span>
                  <span className="text-slate-300 text-sm font-bold">{currentExp}/{maxExp}</span>
                </div>
                <div className="w-full bg-slate-700/50 rounded-full h-2 overflow-hidden">
                  <div
                    className={`h-full bg-gradient-to-r transition-all duration-1000 ${
                      experiencePercent >= 80
                        ? 'from-emerald-400 to-green-500'
                        : experiencePercent >= 50
                          ? 'from-amber-400 to-orange-500'
                          : 'from-blue-400 to-cyan-500'
                    }`}
                    style={{ width: `${experiencePercent}%` }}
                  />
                </div>
                <div className="text-center">
                  <span className="inline-block px-3 py-1 bg-gradient-to-r from-slate-700 to-slate-600 rounded-full text-white text-xs font-semibold">
                    {experiencePercent}% Complete
                  </span>
                </div>
              </div>
            </div>



            {/* Action Buttons - Modern Design */}
            <div className="grid grid-cols-4 gap-3 mt-6">
              <button
                onClick={handleToggleActive}
                disabled={toggleAgentActiveMutation.isPending}
                className={`group relative p-3 rounded-xl transition-all duration-300 ${
                  isActive
                    ? 'bg-gradient-to-br from-emerald-500/20 to-green-600/20 text-emerald-400 hover:from-emerald-500/30 hover:to-green-600/30 shadow-lg shadow-emerald-500/10'
                    : 'bg-gradient-to-br from-slate-600/20 to-slate-700/20 text-slate-400 hover:from-slate-600/30 hover:to-slate-700/30 shadow-lg shadow-slate-500/10'
                } hover:scale-105 active:scale-95 backdrop-blur-sm`}
                title={isActive ? t('common.deactivate') : t('common.activate')}
              >
                <Icon name="power" size="lg" className="drop-shadow-md mx-auto" />
                <div className="absolute inset-0 bg-white/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </button>

              <button
                onClick={handleMemoriesClick}
                className="group relative p-3 rounded-xl bg-gradient-to-br from-blue-500/20 to-indigo-600/20 text-blue-400 hover:from-blue-500/30 hover:to-indigo-600/30 shadow-lg shadow-blue-500/10 transition-all duration-300 hover:scale-105 active:scale-95 backdrop-blur-sm"
                title={t('aiAgents:memories.title', 'Memories')}
              >
                <Icon name="clock" size="lg" className="drop-shadow-md mx-auto" />
                <div className="absolute inset-0 bg-white/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </button>

              <button
                onClick={handleEditAgent}
                className="group relative p-3 rounded-xl bg-gradient-to-br from-amber-500/20 to-orange-600/20 text-amber-400 hover:from-amber-500/30 hover:to-orange-600/30 shadow-lg shadow-amber-500/10 transition-all duration-300 hover:scale-105 active:scale-95 backdrop-blur-sm"
                title={t('common.edit')}
              >
                <Icon name="edit" size="lg" className="drop-shadow-md mx-auto" />
                <div className="absolute inset-0 bg-white/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </button>

              <button
                onClick={handleDeleteClick}
                disabled={deleteAgentMutation.isPending}
                className="group relative p-3 rounded-xl bg-gradient-to-br from-red-500/20 to-rose-600/20 text-red-400 hover:from-red-500/30 hover:to-rose-600/30 shadow-lg shadow-red-500/10 transition-all duration-300 hover:scale-105 active:scale-95 backdrop-blur-sm"
                title={t('common.delete')}
              >
                <Icon name="trash" size="lg" className="drop-shadow-md mx-auto" />
                <div className="absolute inset-0 bg-white/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </button>
            </div>
          </div>
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('deleteConfirmTitle', 'Xác nhận xóa Agent')}
        size="md"
        footer={
          <div className="flex justify-end gap-3">

            <IconCard
              icon="trash"
              title={t('common.delete', 'Xóa')}
              onClick={handleDeleteConfirm}
              variant="danger"
              disabled={deleteAgentMutation.isPending}
            />

          </div>
        }
      >
        <div className="space-y-4">
          

          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={agent.avatar || '/assets/images/default-avatar.png'}
                alt={agent.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{agent.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{agent.typeName}</p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AgentCard;

// Mock data đã được move sang file riêng: ../data/mockAgents.ts
// AgentCardDemo component đã được move sang file riêng để tránh fast refresh warning
