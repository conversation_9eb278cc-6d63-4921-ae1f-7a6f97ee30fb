import {
  Card,
  Icon,
  IconCard,
  Modal,
  Typography,
} from '@/shared/components/common';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AgentListItemDto } from '../api/agent.api';
import { useDeleteAgent, useToggleAgentActive } from '../hooks/useAgent';
import { useAiAgentNotification } from '../hooks/useAiAgentNotification';

// Mock data đã được move sang file riêng: ../data/mockAgents.ts

interface AgentCardProps {
  agent: AgentListItemDto;
  /** Callback khi click memories */
  onMemoriesAgent?: (agentId: string) => void;
}

/**
 * Component hiển thị thông tin của một AI Agent
 */
const AgentCard: React.FC<AgentCardProps> = ({ agent, onMemoriesAgent }) => {
  const { t } = useTranslation('aiAgents');
  const navigate = useNavigate();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { updateSuccess, updateError, deleteSuccess, deleteError } = useAiAgentNotification();

  // Local state để đảm bảo UI cập nhật ngay lập tức
  const [localActive, setLocalActive] = useState(agent.active);

  // Hooks cho API calls
  const deleteAgentMutation = useDeleteAgent();
  const toggleAgentActiveMutation = useToggleAgentActive();

  // Sync local state với props khi props thay đổi
  React.useEffect(() => {
    setLocalActive(agent.active);
  }, [agent.active]);

  // Sử dụng local state để hiển thị
  const isActive = localActive;

  const handleEditAgent = () => {
    // Navigate đến trang edit agent với typeId
    console.log('AgentCard - handleEditAgent:', { agentId: agent.id, typeId: agent.typeId });
    navigate(`/ai-agents/${agent.id}/edit?typeId=${agent.typeId}`);
  };

  const handleMemoriesClick = () => {
    if (onMemoriesAgent) {
      onMemoriesAgent(agent.id);
    }
  };

  const handleToggleActive = async () => {
    const previousState = isActive;

    try {
      // Cập nhật UI ngay lập tức
      setLocalActive(!isActive);

      // Gọi API với optimistic update
      await toggleAgentActiveMutation.mutateAsync(agent.id);

      updateSuccess(
        previousState
          ? t('notification.titles.deactivateSuccess', 'Agent đã được bật')
          : t('notification.titles.activateSuccess', 'Agent đã được tắt')
      );
    } catch (error) {
      console.error('Error toggling agent active:', error);

      // Rollback local state nếu API thất bại
      setLocalActive(previousState);

      updateError(
        'Agent',
        error instanceof Error
          ? error.message
          : t('aiAgents.toggleError', 'Có lỗi xảy ra khi thay đổi trạng thái agent')
      );
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteAgentMutation.mutateAsync(agent.id);
      setShowDeleteModal(false);

      deleteSuccess('Agent');
    } catch (error) {
      console.error('Error deleting agent:', error);
      deleteError('Agent', error instanceof Error ? error.message : undefined);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  // Tính toán phần trăm kinh nghiệm từ dữ liệu thực
  const currentExp = parseInt(agent.exp) || 0;
  const maxExp = parseInt(agent.expMax) || 1;
  const experiencePercent = Math.round((currentExp / maxExp) * 100);



  return (
    <>
      <div className="relative h-full group">
        <Card
          className="h-full overflow-hidden bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 border border-slate-700/50 hover:border-slate-600/70 transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/10 relative"
          variant="elevated"
        >
          {/* Subtle glow effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />

          {/* Progress border */}
          <div className="absolute top-0 left-0 right-0 h-0.5 bg-slate-700 overflow-hidden">
            <div
              className={`h-full bg-gradient-to-r transition-all duration-700 ${
                experiencePercent >= 80
                  ? 'from-emerald-400 to-green-500'
                  : experiencePercent >= 50
                    ? 'from-amber-400 to-orange-500'
                    : 'from-blue-400 to-cyan-500'
              }`}
              style={{ width: `${experiencePercent}%` }}
            />
          </div>
          <div className="p-4 relative z-10">
            {/* Main content */}
            <div className="flex items-center gap-3">
              {/* Avatar section - Tăng kích thước */}
              <div className="relative flex-shrink-0">
                <div className="relative w-20 h-20">
                  {/* Badge frame */}
                  <img
                    src={agent.badgeUrl}
                    alt="Level frame"
                    className="absolute inset-0 w-full h-full object-contain z-10 drop-shadow-lg"
                  />
                  {/* Avatar - Tăng kích thước */}
                  <div className="absolute inset-0 flex items-center justify-center z-0">
                    <img
                      src={agent.avatar || '/assets/images/default-avatar.png'}
                      alt={agent.name}
                      className="w-[72%] h-[72%] rounded-full object-cover shadow-lg ring-2 ring-white/25"
                    />
                  </div>
                  {/* Status indicator */}
                  <div
                    className={`absolute bottom-0 right-0 w-4 h-4 rounded-full z-20 border-2 border-slate-800 shadow-md ${
                      isActive
                        ? 'bg-emerald-400 shadow-emerald-400/50'
                        : 'bg-slate-500'
                    }`}
                  />
                </div>
              </div>

              {/* Info section - Giảm khoảng cách */}
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between mb-1.5">
                  <div className="flex-1 min-w-0">
                    <Typography
                      variant="h6"
                      className="font-semibold text-white truncate text-base leading-tight"
                    >
                      {agent.name}
                    </Typography>
                    <div className="text-xs text-slate-400 font-medium mt-0.5">
                      {agent.modelId}
                    </div>
                  </div>

                  {/* Level badge */}
                  <div className="ml-2 flex-shrink-0">
                    <div className="w-9 h-9 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-white font-bold text-sm">{agent.level}</span>
                    </div>
                  </div>
                </div>

                {/* Experience section - Giảm khoảng cách */}
                <div className="flex items-center gap-2">
                  <div className="px-3 py-1 bg-gradient-to-r from-orange-500 to-red-500 rounded-full shadow-md">
                    <span className="text-white font-semibold text-xs">{experiencePercent}%</span>
                  </div>
                  <span className="text-xs text-slate-400 font-medium">
                    {currentExp}/{maxExp} EXP
                  </span>
                </div>
              </div>
            </div>



            {/* Action Buttons - Kích thước vừa phải */}
            <div className="flex justify-center space-x-4 pt-2">
              <button
                onClick={handleToggleActive}
                disabled={toggleAgentActiveMutation.isPending}
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ${
                  isActive
                    ? 'bg-green-500/10 text-green-500 hover:bg-green-500/20 hover:scale-105'
                    : 'bg-gray-500/10 text-gray-400 dark:text-gray-300 hover:bg-gray-500/20 hover:scale-105'
                }`}
                title={isActive ? t('common.deactivate') : t('common.activate')}
              >
                <Icon name="power" size="md" className="drop-shadow-sm" />
              </button>

              <button
                onClick={handleMemoriesClick}
                className="w-10 h-10 rounded-full bg-blue-500/10 text-blue-500 hover:bg-blue-500/20 hover:scale-105 flex items-center justify-center transition-all duration-300"
                title={t('aiAgents:memories.title', 'Memories')}
              >
                <Icon name="clock" size="md" className="drop-shadow-sm" />
              </button>

              <button
                onClick={handleEditAgent}
                className="w-10 h-10 rounded-full bg-amber-500/10 text-amber-500 hover:bg-amber-500/20 hover:scale-105 flex items-center justify-center transition-all duration-300"
                title={t('common.edit')}
              >
                <Icon name="edit" size="md" className="drop-shadow-sm" />
              </button>

              <button
                onClick={handleDeleteClick}
                disabled={deleteAgentMutation.isPending}
                className="w-10 h-10 rounded-full bg-red-500/10 text-red-500 hover:bg-red-500/20 hover:scale-105 flex items-center justify-center transition-all duration-300"
                title={t('common.delete')}
              >
                <Icon name="trash" size="md" className="drop-shadow-sm" />
              </button>
            </div>
          </div>
        </Card>
      </div>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('deleteConfirmTitle', 'Xác nhận xóa Agent')}
        size="md"
        footer={
          <div className="flex justify-end gap-3">

            <IconCard
              icon="trash"
              title={t('common.delete', 'Xóa')}
              onClick={handleDeleteConfirm}
              variant="danger"
              disabled={deleteAgentMutation.isPending}
            />

          </div>
        }
      >
        <div className="space-y-4">
          

          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={agent.avatar || '/assets/images/default-avatar.png'}
                alt={agent.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{agent.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">{agent.typeName}</p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AgentCard;

// Mock data đã được move sang file riêng: ../data/mockAgents.ts
// AgentCardDemo component đã được move sang file riêng để tránh fast refresh warning
